# 蛋白质温度信息处理程序

这个程序可以批量处理CSV文件中的蛋白质UniProt ID，获取对应的培养温度信息，并支持中断后继续处理。

## 功能特点

1. **实时保存**：每处理完一个蛋白质ID立即保存结果，确保数据不丢失
2. **支持中断后继续**：程序会自动保存进度，中断后重新运行会从上次停止的地方继续
3. **显示进度**：使用进度条显示处理进度
4. **API调用控制**：设置合理的延迟避免API限制
5. **缓存机制**：缓存UniProt查询结果，避免重复请求
6. **错误处理**：对各种异常情况进行处理和记录

## 文件说明

- `process_temperature.py`：主要的处理程序
- `run_temperature_processing.py`：简单的运行接口
- `IDS.csv`：输入文件，包含Entry_ID列
- `progress.json`：自动生成的进度文件
- `organism_cache.json`：自动生成的缓存文件

## 安装依赖

```bash
pip install bacdive pandas tqdm requests
```

## 使用方法

### 基本使用

```bash
python run_temperature_processing.py
```

### 查看处理状态

```bash
python run_temperature_processing.py --status
```

### 重新开始处理（清除进度）

```bash
python run_temperature_processing.py --reset
```

### 自定义参数

```bash
# 使用不同的CSV文件
python run_temperature_processing.py --csv my_proteins.csv

# 设置更长的API调用间隔（3秒）
python run_temperature_processing.py --delay 3.0
```

## 输出格式

程序会在原CSV文件中添加一个新列 `Temperature`，包含以下可能的值：

- `25-37`：温度范围（最小值-最大值）
- `No data`：未找到温度信息
- `No organism`：无法获取organism信息
- `Invalid ID`：无效的UniProt ID
- `Error`：查询过程中出现错误

## 中断和恢复

- 按 `Ctrl+C` 可以随时中断程序
- 程序会自动保存当前进度
- 重新运行程序会从上次中断的地方继续
- **每处理完一个条目立即保存结果**，完全不用担心数据丢失

## 注意事项

1. **API限制**：程序默认设置2秒的API调用间隔，如果遇到限制可以增加延迟时间
2. **网络连接**：确保网络连接稳定，程序会自动重试失败的请求
3. **数据备份**：建议在运行前备份原始CSV文件
4. **长时间运行**：处理大量数据可能需要很长时间，建议在稳定的环境中运行

## 故障排除

### 如果程序卡住不动

1. 检查网络连接
2. 增加API调用延迟：`--delay 5.0`
3. 查看日志输出了解具体问题

### 如果需要重新开始

```bash
python run_temperature_processing.py --reset
```

### 如果某些条目一直失败

程序会标记失败的条目并继续处理其他条目，可以手动检查失败的UniProt ID。

## 示例输出

```
总计: 3986 个蛋白质ID
已处理: 150 个
剩余: 3836 个
进度: 3.8%

处理蛋白质温度信息: 3%|▎| 150/3986 [02:30<1:04:20, 1.01s/it]
```
