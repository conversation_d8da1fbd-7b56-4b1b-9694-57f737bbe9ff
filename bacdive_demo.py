import bacdive
import re
import requests

def get_uniprot_organism(uniprot_id):
    """
    通过UniProt API获取蛋白的主种名（去除括号内容）
    """
    url = f"https://rest.uniprot.org/uniprotkb/{uniprot_id}.json"
    try:
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        # 兼容新版和旧版UniProt结构
        organism = None
        if 'organism' in data and 'scientificName' in data['organism']:
            organism = data['organism']['scientificName']
        elif 'organism' in data and 'name' in data['organism']:
            organism = data['organism']['name']
        if organism:
            # 去除括号及其内容
            organism = re.sub(r'\s*\([^)]*\)', '', organism).strip()
        return organism
    except Exception as e:
        print(f"获取UniProt {uniprot_id} 宿主失败: {e}")
        return None

def extract_temps(temp):
    """
    支持单值和区间（如28-30），返回所有温度的int列表
    """
    if not temp:
        return []
    matches = re.findall(r'(\d+)(?:-(\d+))?', str(temp))
    result = []
    for m in matches:
        if m[1]:
            result.extend([int(m[0]), int(m[1])])
        else:
            result.append(int(m[0]))
    return result

client = bacdive.BacdiveClient('<EMAIL>', '789456123xia@TJ')
client.setSearchType('exact')

# 输入UniProt蛋白ID列表
uniprot_ids = [
    'P0A6F5',  # 例如 E. coli
    # 可添加更多
]

for uniprot_id in uniprot_ids:
    print(f"\nUniProt ID: {uniprot_id}")
    organism_name = get_uniprot_organism(uniprot_id)
    if not organism_name:
        print("未能获取宿主名称")
        continue
    print(f"宿主主种名: {organism_name}")
    count = client.search(taxonomy=organism_name)
    print(count, 'strains found.')
    temps = []
    for strain in client.retrieve():
        temp = None
        culture = strain.get('Culture and growth conditions', {})
        culture_temp = culture.get('culture temp', {})
        if isinstance(culture_temp, dict):
            temp = culture_temp.get('temperature')
        elif isinstance(culture_temp, list):
            for item in culture_temp:
                if isinstance(item, dict) and 'temperature' in item:
                    temp = item['temperature']
                    break
        if not temp and 'culture_growth_condition' in strain:
            growth_conditions = strain['culture_growth_condition']
            if isinstance(growth_conditions, list):
                for condition in growth_conditions:
                    if 'temperature' in condition:
                        temp = condition['temperature']
                        break
            elif isinstance(growth_conditions, dict):
                temp = growth_conditions.get('temperature')
        if not temp:
            temp = strain.get('growth_temperature')
        if not temp:
            temp = strain.get('temperature')
        temps.extend(extract_temps(temp))
    if temps:
        print(f"生长温度: {min(temps)}--{max(temps)}℃")
    else:
        print("未找到生长温度信息")
