#!/usr/bin/env python3
"""
调试BacDive API返回的JSON结构，找出正确的温度信息提取方式
"""

import bacdive
import json
import pprint
from process_temperature import TemperatureProcessor

def debug_bacdive_response():
    """调试BacDive API响应结构"""
    
    # 初始化客户端
    client = bacdive.BacdiveClient('<EMAIL>', '789456123xia@TJ')
    client.setSearchType('exact')
    
    # 测试一个已知有数据的organism
    test_organisms = [
        "Escherichia coli",
        "Vibrio nigripulchritudo", 
        "Bacillus subtilis"
    ]
    
    for organism in test_organisms:
        print(f"\n{'='*60}")
        print(f"调试 organism: {organism}")
        print(f"{'='*60}")
        
        try:
            count = client.search(taxonomy=organism)
            print(f"找到 {count} 个菌株")
            
            if count == 0:
                print("没有找到菌株数据")
                continue
            
            # 只检查前2个菌株的详细结构
            strain_count = 0
            for strain in client.retrieve():
                strain_count += 1
                if strain_count > 2:  # 只看前2个
                    break
                    
                print(f"\n--- 菌株 {strain_count} ---")
                
                # 打印整个strain的键
                print("strain的顶级键:")
                for key in strain.keys():
                    print(f"  - {key}")
                
                # 检查Culture and growth conditions
                if 'Culture and growth conditions' in strain:
                    culture = strain['Culture and growth conditions']
                    print(f"\nCulture and growth conditions 类型: {type(culture)}")
                    if isinstance(culture, dict):
                        print("Culture and growth conditions 的键:")
                        for key in culture.keys():
                            print(f"  - {key}")
                        
                        # 检查culture temp
                        if 'culture temp' in culture:
                            culture_temp = culture['culture temp']
                            print(f"\nculture temp 类型: {type(culture_temp)}")
                            print(f"culture temp 内容:")
                            pprint.pprint(culture_temp, indent=2)
                    elif isinstance(culture, list):
                        print(f"Culture and growth conditions 是列表，长度: {len(culture)}")
                        for i, item in enumerate(culture):
                            print(f"  项目 {i}: {type(item)}")
                            if isinstance(item, dict):
                                print(f"    键: {list(item.keys())}")
                
                # 检查其他可能包含温度的字段
                temp_related_keys = []
                for key in strain.keys():
                    if 'temp' in key.lower() or 'growth' in key.lower() or 'culture' in key.lower():
                        temp_related_keys.append(key)
                
                if temp_related_keys:
                    print(f"\n可能包含温度信息的键:")
                    for key in temp_related_keys:
                        print(f"  - {key}: {type(strain[key])}")
                        if isinstance(strain[key], (dict, list)) and len(str(strain[key])) < 500:
                            print(f"    内容: {strain[key]}")
                
                # 保存完整的strain数据到文件以便详细分析
                with open(f'strain_{organism.replace(" ", "_")}_{strain_count}.json', 'w', encoding='utf-8') as f:
                    json.dump(strain, f, ensure_ascii=False, indent=2)
                print(f"\n完整数据已保存到: strain_{organism.replace(' ', '_')}_{strain_count}.json")
                
        except Exception as e:
            print(f"处理 {organism} 时出错: {e}")

def test_temperature_extraction():
    """测试当前的温度提取逻辑"""
    print(f"\n{'='*60}")
    print("测试当前的温度提取逻辑")
    print(f"{'='*60}")
    
    processor = TemperatureProcessor(delay=1)
    
    # 测试几个organism
    test_organisms = ["Escherichia coli", "Vibrio nigripulchritudo"]
    
    for organism in test_organisms:
        print(f"\n测试 {organism}:")
        result = processor.get_temperature_range(organism)
        print(f"结果: {result}")

if __name__ == "__main__":
    print("开始调试BacDive API响应结构...")
    debug_bacdive_response()
    test_temperature_extraction()
