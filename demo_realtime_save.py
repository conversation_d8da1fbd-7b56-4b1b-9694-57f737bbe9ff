#!/usr/bin/env python3
"""
演示实时保存功能的脚本
"""

import pandas as pd
import time
import os
from process_temperature import TemperatureProcessor

def demo_realtime_save():
    """演示实时保存功能"""
    
    print("=== 实时保存功能演示 ===\n")
    
    # 创建一个小的演示文件
    demo_data = {
        'Entry_ID': ['P0A6F5', 'Q9KVI8', 'invalid_id'],  # 混合有效和无效ID
        'Temperature': ['', '', '']
    }
    
    demo_file = 'demo_realtime.csv'
    df = pd.DataFrame(demo_data)
    df.to_csv(demo_file, index=False)
    
    print(f"创建演示文件: {demo_file}")
    print("包含的蛋白质ID:")
    for i, protein_id in enumerate(demo_data['Entry_ID'], 1):
        print(f"  {i}. {protein_id}")
    
    print(f"\n开始处理，每个条目处理完后会立即保存到 {demo_file}")
    print("您可以在另一个终端中运行以下命令实时查看文件变化:")
    print(f"  watch -n 1 'cat {demo_file}'")
    print("\n按 Enter 开始演示...")
    input()
    
    # 创建处理器
    processor = TemperatureProcessor(
        csv_file=demo_file,
        progress_file='demo_progress.json',
        cache_file='demo_cache.json',
        delay=1  # 较短延迟用于演示
    )
    
    try:
        processor.process_all()
        
        print(f"\n演示完成！查看最终结果:")
        df_result = pd.read_csv(demo_file)
        print(df_result.to_string(index=False))
        
        print(f"\n文件 {demo_file} 已包含所有结果")
        print("注意：每个条目都是处理完后立即保存的，不会丢失任何数据！")
        
    except KeyboardInterrupt:
        print(f"\n演示被中断，但已处理的数据已保存在 {demo_file} 中")
        df_result = pd.read_csv(demo_file)
        print("当前保存的结果:")
        print(df_result.to_string(index=False))
    
    # 清理演示文件
    cleanup = input("\n是否删除演示文件? (y/N): ").lower().strip()
    if cleanup == 'y':
        for file in [demo_file, 'demo_progress.json', 'demo_cache.json']:
            if os.path.exists(file):
                os.remove(file)
                print(f"已删除 {file}")

if __name__ == "__main__":
    demo_realtime_save()
