#!/usr/bin/env python3
"""
监控处理进度的脚本
"""

import pandas as pd
import time
import json
import os
from datetime import datetime

def monitor_progress():
    """监控处理进度"""
    
    print("蛋白质温度处理进度监控")
    print("=" * 50)
    
    while True:
        try:
            # 读取CSV文件
            df = pd.read_csv('IDS.csv')
            total_count = len(df)
            
            # 统计各种结果
            processed_count = 0
            temp_ranges = 0
            errors = 0
            no_organism = 0
            no_data = 0
            empty = 0
            
            for temp in df['Temperature']:
                if pd.isna(temp) or temp == '':
                    empty += 1
                else:
                    processed_count += 1
                    if 'Error' in str(temp):
                        errors += 1
                    elif 'No organism' in str(temp):
                        no_organism += 1
                    elif 'No data' in str(temp):
                        no_data += 1
                    elif '-' in str(temp) and str(temp) != 'No data':
                        temp_ranges += 1
            
            # 读取进度文件
            progress_info = ""
            if os.path.exists('progress.json'):
                with open('progress.json', 'r') as f:
                    progress = json.load(f)
                    last_index = progress.get('last_index', -1)
                    progress_info = f"最后处理索引: {last_index}"
            
            # 显示统计信息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n[{current_time}] 处理进度统计:")
            print(f"总计: {total_count} 个蛋白质ID")
            print(f"已处理: {processed_count} 个 ({processed_count/total_count*100:.1f}%)")
            print(f"剩余: {total_count - processed_count} 个")
            print(f"{progress_info}")
            print()
            print("结果分布:")
            print(f"  ✅ 成功获取温度: {temp_ranges} 个")
            print(f"  ❌ 错误/无数据: {errors} 个")
            print(f"  🚫 无organism: {no_organism} 个")
            print(f"  📭 无温度数据: {no_data} 个")
            print(f"  ⏳ 未处理: {empty} 个")
            
            if temp_ranges > 0:
                success_rate = temp_ranges / processed_count * 100 if processed_count > 0 else 0
                print(f"\n成功率: {success_rate:.1f}% (在已处理的条目中)")
            
            # 显示最近的成功案例
            recent_success = []
            for idx, row in df.iterrows():
                temp = row['Temperature']
                if pd.notna(temp) and '-' in str(temp) and 'Error' not in str(temp) and 'No' not in str(temp):
                    recent_success.append(f"{row['Entry_ID']}: {temp}")
            
            if recent_success:
                print(f"\n最近成功的温度提取 (共{len(recent_success)}个):")
                for success in recent_success[-5:]:  # 显示最后5个
                    print(f"  {success}")
            
            print("\n" + "=" * 50)
            
            # 等待30秒后再次检查
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控出错: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_progress()
