import bacdive
import re
import requests
import pandas as pd
import time
import json
import os
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TemperatureProcessor:
    def __init__(self, csv_file='IDS.csv', progress_file='progress.json', 
                 cache_file='organism_cache.json', delay=2):
        """
        初始化温度处理器
        
        Args:
            csv_file: 包含Entry_ID列的CSV文件
            progress_file: 保存进度的JSON文件
            cache_file: 缓存organism信息的JSON文件
            delay: API调用间隔（秒）
        """
        self.csv_file = csv_file
        self.progress_file = progress_file
        self.cache_file = cache_file
        self.delay = delay
        
        # 初始化BacDive客户端
        self.client = bacdive.BacdiveClient('<EMAIL>', '789456123xia@TJ')
        self.client.setSearchType('exact')
        
        # 加载数据
        self.df = pd.read_csv(csv_file)
        if 'Temperature' not in self.df.columns:
            self.df['Temperature'] = ''
        
        # 加载进度和缓存
        self.progress = self.load_progress()
        self.organism_cache = self.load_cache()
        
    def load_progress(self):
        """加载处理进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                logger.warning(f"无法加载进度文件 {self.progress_file}")
        return {'processed_indices': [], 'last_index': -1}
    
    def save_progress(self):
        """保存处理进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            json.dump(self.progress, f, ensure_ascii=False, indent=2)
    
    def load_cache(self):
        """加载organism缓存"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                logger.warning(f"无法加载缓存文件 {self.cache_file}")
        return {}
    
    def save_cache(self):
        """保存organism缓存"""
        with open(self.cache_file, 'w', encoding='utf-8') as f:
            json.dump(self.organism_cache, f, ensure_ascii=False, indent=2)
    
    def get_uniprot_organism(self, uniprot_id):
        """
        通过UniProt API获取蛋白的主种名（去除括号内容）
        使用缓存避免重复请求
        """
        if uniprot_id in self.organism_cache:
            return self.organism_cache[uniprot_id]
        
        url = f"https://rest.uniprot.org/uniprotkb/{uniprot_id}.json"
        try:
            time.sleep(self.delay)  # API调用延迟
            resp = requests.get(url, timeout=10)
            resp.raise_for_status()
            data = resp.json()
            
            # 兼容新版和旧版UniProt结构
            organism = None
            if 'organism' in data and 'scientificName' in data['organism']:
                organism = data['organism']['scientificName']
            elif 'organism' in data and 'name' in data['organism']:
                organism = data['organism']['name']
            
            if organism:
                # 去除括号及其内容
                organism = re.sub(r'\s*\([^)]*\)', '', organism).strip()
            
            # 缓存结果
            self.organism_cache[uniprot_id] = organism
            self.save_cache()
            
            return organism
        except Exception as e:
            logger.error(f"获取UniProt {uniprot_id} 宿主失败: {e}")
            self.organism_cache[uniprot_id] = None
            self.save_cache()
            return None
    
    def extract_temps(self, temp):
        """
        支持单值和区间（如28-30），返回所有温度的int列表
        """
        if not temp:
            return []
        matches = re.findall(r'(\d+)(?:-(\d+))?', str(temp))
        result = []
        for m in matches:
            if m[1]:
                result.extend([int(m[0]), int(m[1])])
            else:
                result.append(int(m[0]))
        return result
    
    def get_temperature_range(self, organism_name):
        """
        从BacDive数据库获取指定organism的温度范围
        """
        if not organism_name:
            return None
        
        try:
            time.sleep(self.delay)  # API调用延迟
            count = self.client.search(taxonomy=organism_name)
            logger.info(f"找到 {count} 个 {organism_name} 菌株")
            
            temps = []
            for strain in self.client.retrieve():
                temp = None
                
                # 尝试多种方式获取温度信息
                culture = strain.get('Culture and growth conditions', {})
                culture_temp = culture.get('culture temp', {})
                
                if isinstance(culture_temp, dict):
                    temp = culture_temp.get('temperature')
                elif isinstance(culture_temp, list):
                    for item in culture_temp:
                        if isinstance(item, dict) and 'temperature' in item:
                            temp = item['temperature']
                            break
                
                if not temp and 'culture_growth_condition' in strain:
                    growth_conditions = strain['culture_growth_condition']
                    if isinstance(growth_conditions, list):
                        for condition in growth_conditions:
                            if 'temperature' in condition:
                                temp = condition['temperature']
                                break
                    elif isinstance(growth_conditions, dict):
                        temp = growth_conditions.get('temperature')
                
                if not temp:
                    temp = strain.get('growth_temperature')
                if not temp:
                    temp = strain.get('temperature')
                
                temps.extend(self.extract_temps(temp))
            
            if temps:
                min_temp = min(temps)
                max_temp = max(temps)
                return f"{min_temp}-{max_temp}"
            else:
                return "No data"
                
        except Exception as e:
            logger.error(f"获取 {organism_name} 温度信息失败: {e}")
            return "Error"
    
    def process_all(self):
        """
        处理所有蛋白质ID，支持中断后继续
        """
        total_count = len(self.df)
        processed_count = len(self.progress['processed_indices'])
        
        logger.info(f"总共 {total_count} 个蛋白质ID，已处理 {processed_count} 个")
        
        # 创建进度条
        with tqdm(total=total_count, initial=processed_count, 
                  desc="处理蛋白质温度信息") as pbar:
            
            for idx, row in self.df.iterrows():
                # 跳过已处理的条目
                if idx in self.progress['processed_indices']:
                    continue
                
                uniprot_id = str(row['Entry_ID']).strip()
                
                # 跳过无效ID
                if not uniprot_id or uniprot_id == 'nan':
                    self.df.at[idx, 'Temperature'] = 'Invalid ID'
                    self.progress['processed_indices'].append(idx)
                    self.progress['last_index'] = idx
                    # 立即保存无效ID的结果
                    self.save_progress()
                    self.save_csv()
                    logger.info(f"已保存 {uniprot_id} 的结果: Invalid ID")
                    pbar.update(1)
                    continue
                
                logger.info(f"处理 {uniprot_id} ({idx+1}/{total_count})")
                
                # 获取organism信息
                organism_name = self.get_uniprot_organism(uniprot_id)
                if not organism_name:
                    self.df.at[idx, 'Temperature'] = 'No organism'
                    self.progress['processed_indices'].append(idx)
                    self.progress['last_index'] = idx
                    # 立即保存无organism的结果
                    self.save_progress()
                    self.save_csv()
                    logger.info(f"已保存 {uniprot_id} 的结果: No organism")
                    pbar.update(1)
                    continue
                
                logger.info(f"Organism: {organism_name}")
                
                # 获取温度范围
                temp_range = self.get_temperature_range(organism_name)
                self.df.at[idx, 'Temperature'] = temp_range or 'No data'

                # 更新进度
                self.progress['processed_indices'].append(idx)
                self.progress['last_index'] = idx

                # 每处理一个条目就立即保存
                self.save_progress()
                self.save_csv()
                logger.info(f"已保存 {uniprot_id} 的结果: {temp_range or 'No data'}")

                pbar.update(1)
        
        # 最终保存
        self.save_progress()
        self.save_csv()
        logger.info("处理完成！")
    
    def save_csv(self):
        """保存CSV文件"""
        self.df.to_csv(self.csv_file, index=False, encoding='utf-8')
    
    def reset_progress(self):
        """重置进度（重新开始处理）"""
        self.progress = {'processed_indices': [], 'last_index': -1}
        self.save_progress()
        logger.info("进度已重置")

if __name__ == "__main__":
    processor = TemperatureProcessor(delay=2)  # 2秒延迟避免API限制
    
    # 如果需要重新开始，取消下面这行的注释
    # processor.reset_progress()
    
    processor.process_all()
