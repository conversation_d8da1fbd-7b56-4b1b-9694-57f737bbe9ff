#!/usr/bin/env python3
"""
运行温度处理脚本的简单接口
"""

import argparse
from process_temperature import TemperatureProcessor

def main():
    parser = argparse.ArgumentParser(description='处理蛋白质温度信息')
    parser.add_argument('--csv', default='IDS.csv', help='输入CSV文件路径')
    parser.add_argument('--delay', type=float, default=2.0, help='API调用间隔（秒）')
    parser.add_argument('--reset', action='store_true', help='重置进度，重新开始处理')
    parser.add_argument('--status', action='store_true', help='显示当前处理状态')
    
    args = parser.parse_args()
    
    processor = TemperatureProcessor(csv_file=args.csv, delay=args.delay)
    
    if args.status:
        # 显示状态
        total = len(processor.df)
        processed = len(processor.progress['processed_indices'])
        print(f"总计: {total} 个蛋白质ID")
        print(f"已处理: {processed} 个")
        print(f"剩余: {total - processed} 个")
        print(f"进度: {processed/total*100:.1f}%")
        return
    
    if args.reset:
        processor.reset_progress()
        print("进度已重置")
    
    print("开始处理温度信息...")
    print("提示：可以随时按 Ctrl+C 中断，下次运行会从中断处继续")
    
    try:
        processor.process_all()
        print("处理完成！结果已保存到", args.csv)
    except KeyboardInterrupt:
        print("\n处理被中断，进度已保存。下次运行会从中断处继续。")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        print("进度已保存，可以重新运行继续处理。")

if __name__ == "__main__":
    main()
