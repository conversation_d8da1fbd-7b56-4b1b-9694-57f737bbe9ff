#!/usr/bin/env python3
"""
测试改进后的温度提取逻辑
"""

import logging
from process_temperature import TemperatureProcessor

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def test_improved_extraction():
    """测试改进后的温度提取"""
    
    processor = TemperatureProcessor(delay=1)
    
    # 测试已知有温度数据的organisms
    test_cases = [
        "Escherichia coli",
        "Bacillus subtilis", 
        "Vibrio nigripulchritudo",
        "Staphylococcus aureus"
    ]
    
    print("测试改进后的温度提取逻辑:")
    print("="*50)
    
    for organism in test_cases:
        print(f"\n测试 {organism}:")
        result = processor.get_temperature_range(organism)
        print(f"结果: {result}")
        print("-" * 30)

if __name__ == "__main__":
    test_improved_extraction()
