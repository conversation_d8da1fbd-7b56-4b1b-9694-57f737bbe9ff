#!/usr/bin/env python3
"""
测试真实的UniProt ID的完整流程
"""

import pandas as pd
from process_temperature import TemperatureProcessor

def test_real_uniprot_ids():
    """测试真实的UniProt ID"""
    
    # 从IDS.csv中选择一些ID进行测试
    df = pd.read_csv('IDS.csv')
    
    # 选择一些还没有处理过的ID
    test_ids = ['P0A6F5', 'Q9KVI8', 'U4KBC9', 'U4K7W9', 'Q6LS62']
    
    print("测试真实UniProt ID的完整流程:")
    print("="*50)
    
    processor = TemperatureProcessor(delay=1)
    
    for uniprot_id in test_ids:
        print(f"\n测试 UniProt ID: {uniprot_id}")
        print("-" * 30)
        
        # 获取organism
        organism = processor.get_uniprot_organism(uniprot_id)
        print(f"Organism: {organism}")
        
        if organism:
            # 获取温度范围
            temp_range = processor.get_temperature_range(organism)
            print(f"温度范围: {temp_range}")
        else:
            print("无法获取organism信息")
        
        print()

if __name__ == "__main__":
    test_real_uniprot_ids()
