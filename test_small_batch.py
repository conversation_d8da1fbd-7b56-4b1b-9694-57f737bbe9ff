#!/usr/bin/env python3
"""
测试脚本：处理前几个蛋白质ID来验证程序功能
"""

import pandas as pd
from process_temperature import TemperatureProcessor

def test_small_batch(num_entries=5):
    """测试处理前几个条目"""

    # 读取原始数据
    df_original = pd.read_csv('IDS.csv')

    # 创建测试数据（跳过前面已测试的，取新的条目）
    df_test = df_original.iloc[10:10+num_entries].copy()  # 从第11个开始
    df_test['Temperature'] = ''

    # 保存测试文件
    test_file = 'test_new_IDS.csv'
    df_test.to_csv(test_file, index=False)
    
    print(f"创建测试文件 {test_file}，包含 {num_entries} 个条目")
    print("测试条目:")
    for idx, row in df_test.iterrows():
        print(f"  {idx+1}. {row['Entry_ID']}")
    
    # 处理测试数据
    processor = TemperatureProcessor(csv_file=test_file,
                                   progress_file='test_progress.json',
                                   cache_file='test_cache.json',
                                   delay=1)  # 较短延迟用于测试

    try:
        processor.process_all()

        # 显示结果
        df_result = pd.read_csv(test_file)
        print("\n测试结果:")
        for idx, row in df_result.iterrows():
            print(f"  {row['Entry_ID']}: {row['Temperature']}")
            
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    test_small_batch(3)  # 测试前3个条目
